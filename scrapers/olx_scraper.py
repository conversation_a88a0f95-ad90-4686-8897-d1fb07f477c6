import re
import time
import traceback
from playwright.sync_api import Browser

# Lista dostępnych kategorii na OLX
OLX_CATEGORIES = [
    "/antyki-i-kolekcje/",
    "/firma-i-przemysl/",
    "/motoryzacja/",
    "/nieruchomosci/",
    "/praca/",
    "/dom-i-ogrod/",
    "/elektronika/",
    "/moda/",
    "/rolnictwo/",
    "/zwierzeta/",
    "/dla-dzieci/",
    "/sport-i-hobby/",
    "/muzyka-i-edukacja/",
    "/zdrowie-i-uroda/",
    "/uslugi/",
    "/noclegi/",
    "/wypozyczalnia/",
    "/oddamo-za-darmo/"
]



def search(browser: Browser, query: str, category: str = "") -> list[dict]:
    """Wyszukuje oferty na OLX i zwraca listę słowników z detalami."""
    offers = []
    page = browser.new_page()

    try:
        # Konstruuj URL z kategorią lub bez niej
        if category and category in OLX_CATEGORIES:
            search_url = f"https://www.olx.pl{category}q-{query}/"
            print(f"Wyszukuję na OLX w kategorii {category}: {search_url}")
        else:
            search_url = f"https://www.olx.pl/oferty/q-{query}/"
            print(f"Wyszukuję na OLX (wszystkie kategorie): {search_url}")

        page.goto(search_url, wait_until="domcontentloaded")
        time.sleep(3) # Początkowe oczekiwanie na załadowanie

        # Scrollowanie, aby załadować więcej ofert
        for _ in range(2): # Scrolluj 2 razy, aby załadować więcej wyników
            page.mouse.wheel(0, 10000) # Scrolluj w dół o 10000 pikseli
            time.sleep(2)

        # Selektory dla ofert na OLX - nowa struktura
        # Szukamy kontenerów ofert z tytułami i cenami
        offer_elements = page.locator("div[data-cy='ad-card-title']").all()
        print(f"Znaleziono {len(offer_elements)} potencjalnych ofert na OLX.")

        for offer_element in offer_elements:
            try:
                # Tytuł z h4 wewnątrz kontenera
                title_element = offer_element.locator("h4")
                title = title_element.inner_text().strip()

                # URL z linka w tytule
                link_element = offer_element.locator("a")
                relative_url = link_element.get_attribute("href")
                url = f"https://www.olx.pl{relative_url}" if relative_url and not relative_url.startswith("http") else relative_url

                # Cena z elementu data-testid="ad-price"
                price_element = offer_element.locator("p[data-testid='ad-price']")
                price_text = price_element.inner_text().strip()

                # Wyciągnij liczbę z tekstu ceny (usuń "zł", "do negocjacji" itp.)
                price_match = re.search(r'(\d+(?:[,\s]\d{3})*(?:[,.]\d{2})?)', price_text.replace(' ', ''))
                if price_match:
                    price_str = price_match.group(1).replace(',', '.').replace(' ', '')
                    price = float(price_str)
                else:
                    print(f"Nie udało się sparsować ceny: {price_text}")
                    continue

                if title and price and url:
                    offers.append({"title": title, "price": price, "url": url})
                    print(f"Znaleziono ofertę OLX: {title} - {price} zł")

            except Exception as e:
                # Błąd parsowania pojedynczej oferty, kontynuujemy z następną
                print(f"Błąd podczas parsowania pojedynczej oferty OLX: {e}")
                print(traceback.format_exc())
                continue

    except Exception as e:
        print(f"Wystąpił błąd podczas wyszukiwania na OLX: {e}")
        print(traceback.format_exc())
    finally:
        page.close()
    return offers
