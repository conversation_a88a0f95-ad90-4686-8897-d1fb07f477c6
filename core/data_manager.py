import sqlite3
from config.settings import DATABASE_PATH

def get_db_connection():
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def initialize_database():
    """Tworzy tabele, jeśli nie istnieją."""
    conn = get_db_connection()

    # Tabela dla śledzenia przetworzonych ofert
    conn.execute("""
        CREATE TABLE IF NOT EXISTS processed_offers (
            id TEXT PRIMARY KEY,
            processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)

    # Tabela dla szczegółowych wyników analizy
    conn.execute("""
        CREATE TABLE IF NOT EXISTS analysis_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            offer_id TEXT NOT NULL,
            facebook_title TEXT,
            facebook_price REAL,
            facebook_url TEXT,
            facebook_description TEXT,
            search_queries TEXT,
            is_comparable BOOLEAN,
            olx_category TEXT,
            olx_offers_count INTEGER,
            analysis_text TEXT,
            is_profitable BOOLEAN,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (offer_id) REFERENCES processed_offers (id)
        )
    """)

    # Dodaj kolumnę olx_category jeśli nie istnieje (dla istniejących baz danych)
    try:
        conn.execute("ALTER TABLE analysis_results ADD COLUMN olx_category TEXT")
        print("Dodano kolumnę olx_category do tabeli analysis_results")
    except sqlite3.OperationalError:
        # Kolumna już istnieje
        pass

    conn.commit()
    conn.close()

def is_offer_processed(offer_id: str) -> bool:
    """Sprawdza, czy oferta była już przetwarzana."""
    conn = get_db_connection()
    cursor = conn.execute("SELECT id FROM processed_offers WHERE id = ?", (offer_id,))
    result = cursor.fetchone()
    conn.close()
    return result is not None

def mark_offer_as_processed(offer_id: str):
    """Zaznacza ofertę jako przetworzoną."""
    conn = get_db_connection()
    conn.execute("INSERT INTO processed_offers (id) VALUES (?)", (offer_id,))
    conn.commit()
    conn.close()

def save_analysis_result(offer_details: dict, search_data: dict, olx_offers: list, analysis_result: dict):
    """Zapisuje szczegółowe wyniki analizy do bazy danych."""
    conn = get_db_connection()

    # Konwertuj zapytania wyszukiwania na string JSON
    import json
    search_queries_json = json.dumps(search_data.get('queries', []))

    # Wyciągnij dane z analysis_result
    analysis_text = analysis_result.get('analysis', 'Brak analizy')
    is_profitable = analysis_result.get('isProfitable', False)

    conn.execute("""
        INSERT INTO analysis_results (
            offer_id, facebook_title, facebook_price, facebook_url, facebook_description,
            search_queries, is_comparable, olx_category, olx_offers_count, analysis_text, is_profitable
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, (
        offer_details.get('id'),
        offer_details.get('title'),
        offer_details.get('price'),
        offer_details.get('url'),
        offer_details.get('description'),
        search_queries_json,
        search_data.get('isComparable', False),
        search_data.get('olxCategory', ''),
        len(olx_offers),
        analysis_text,
        is_profitable
    ))

    conn.commit()
    conn.close()

    profitable_status = "ZYSKOWNA" if is_profitable else "NIEZYSKOWNA"
    print(f"Zapisano wyniki analizy dla oferty {offer_details.get('id')} - {profitable_status}")

def get_analysis_history(limit: int = 50):
    """Pobiera historię analiz z bazy danych."""
    conn = get_db_connection()
    cursor = conn.execute("""
        SELECT * FROM analysis_results
        ORDER BY created_at DESC
        LIMIT ?
    """, (limit,))
    results = cursor.fetchall()
    conn.close()
    return [dict(row) for row in results]
